package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 盘点计划服务测试类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StocktakingPlanServiceTest {

    @Resource
    private StocktakingPlanService planService;

    private StocktakingPlanDto testPlanDto;
    private String testPlanId;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testPlanDto = new StocktakingPlanDto();
        testPlanDto.setPlanName("测试盘点计划");
        testPlanDto.setPlanType(AssetStocktakingPlan.PlanType.FULL);
        testPlanDto.setStartDate(new Date());
        testPlanDto.setEndDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L)); // 7天后
        testPlanDto.setResponsibleUserId(1L);
        testPlanDto.setRemark("这是一个测试盘点计划");

        // 设置盘点范围
        StocktakingPlanDto.PlanScopeDetail scopeDetail = new StocktakingPlanDto.PlanScopeDetail();
        scopeDetail.setIncludeSubDept(true);
        scopeDetail.setMinValue(0.0);
        scopeDetail.setMaxValue(100000.0);
        testPlanDto.setScopeDetail(scopeDetail);
    }

    @Test
    void testCreatePlan() {
        // 测试创建盘点计划
        boolean result = planService.createPlan(testPlanDto);
        assertTrue(result, "创建盘点计划应该成功");
    }

    @Test
    void testCreatePlanWithInvalidData() {
        // 测试无效数据创建计划
        StocktakingPlanDto invalidDto = new StocktakingPlanDto();
        invalidDto.setPlanName(""); // 空名称
        
        boolean result = planService.createPlan(invalidDto);
        assertFalse(result, "无效数据创建计划应该失败");
    }

    @Test
    void testCreatePlanWithDuplicateName() {
        // 先创建一个计划
        planService.createPlan(testPlanDto);
        
        // 再创建同名计划
        StocktakingPlanDto duplicateDto = new StocktakingPlanDto();
        duplicateDto.setPlanName(testPlanDto.getPlanName());
        duplicateDto.setPlanType(AssetStocktakingPlan.PlanType.PARTIAL);
        duplicateDto.setStartDate(new Date());
        duplicateDto.setEndDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        duplicateDto.setResponsibleUserId(1L);
        
        boolean result = planService.createPlan(duplicateDto);
        assertFalse(result, "重复名称的计划创建应该失败");
    }

    @Test
    void testEditPlan() {
        // 先创建计划
        planService.createPlan(testPlanDto);
        
        // 查询创建的计划
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        assertFalse(page.getRecords().isEmpty(), "应该能查询到创建的计划");
        
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        // 修改计划
        testPlanDto.setPlanId(testPlanId);
        testPlanDto.setPlanName("修改后的测试计划");
        testPlanDto.setRemark("修改后的备注");
        
        boolean result = planService.editPlan(testPlanDto);
        assertTrue(result, "修改计划应该成功");
        
        // 验证修改结果
        StocktakingPlanVo updatedPlan = planService.selectPlanById(testPlanId);
        assertEquals("修改后的测试计划", updatedPlan.getPlanName(), "计划名称应该已修改");
        assertEquals("修改后的备注", updatedPlan.getRemark(), "备注应该已修改");
    }

    @Test
    void testDeletePlan() {
        // 先创建计划
        planService.createPlan(testPlanDto);
        
        // 查询创建的计划
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        // 删除计划
        boolean result = planService.deletePlan(testPlanId);
        assertTrue(result, "删除计划应该成功");
        
        // 验证删除结果
        StocktakingPlanVo deletedPlan = planService.selectPlanById(testPlanId);
        assertNull(deletedPlan, "删除后应该查询不到计划");
    }

    @Test
    void testSelectPlanList() {
        // 创建多个测试计划
        for (int i = 1; i <= 3; i++) {
            StocktakingPlanDto dto = new StocktakingPlanDto();
            dto.setPlanName("测试计划" + i);
            dto.setPlanType(AssetStocktakingPlan.PlanType.FULL);
            dto.setStartDate(new Date());
            dto.setEndDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
            dto.setResponsibleUserId(1L);
            planService.createPlan(dto);
        }
        
        // 查询计划列表
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName("测试计划");
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        assertTrue(page.getRecords().size() >= 3, "应该查询到至少3个计划");
    }

    @Test
    void testSubmitForApproval() {
        // 先创建计划
        planService.createPlan(testPlanDto);
        
        // 查询创建的计划
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        // 提交审批
        boolean result = planService.submitForApproval(testPlanId);
        assertTrue(result, "提交审批应该成功");
        
        // 验证状态变更
        StocktakingPlanVo updatedPlan = planService.selectPlanById(testPlanId);
        assertEquals(AssetStocktakingPlan.Status.PENDING_APPROVAL, updatedPlan.getStatus(), 
                    "状态应该变为待审批");
    }

    @Test
    void testApprovePlan() {
        // 先创建并提交审批
        planService.createPlan(testPlanDto);
        
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        planService.submitForApproval(testPlanId);
        
        // 审批通过
        boolean result = planService.approvePlan(testPlanId, "审批通过");
        assertTrue(result, "审批通过应该成功");
        
        // 验证状态变更
        StocktakingPlanVo approvedPlan = planService.selectPlanById(testPlanId);
        assertEquals(AssetStocktakingPlan.Status.IN_PROGRESS, approvedPlan.getStatus(), 
                    "状态应该变为执行中");
    }

    @Test
    void testRejectPlan() {
        // 先创建并提交审批
        planService.createPlan(testPlanDto);
        
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        planService.submitForApproval(testPlanId);
        
        // 审批拒绝
        boolean result = planService.rejectPlan(testPlanId, "需要修改盘点范围");
        assertTrue(result, "审批拒绝应该成功");
        
        // 验证状态变更
        StocktakingPlanVo rejectedPlan = planService.selectPlanById(testPlanId);
        assertEquals(AssetStocktakingPlan.Status.DRAFT, rejectedPlan.getStatus(), 
                    "状态应该变回草稿");
    }

    @Test
    void testCheckPlanNameExists() {
        // 先创建计划
        planService.createPlan(testPlanDto);
        
        // 检查名称是否存在
        boolean exists = planService.checkPlanNameExists(testPlanDto.getPlanName(), null);
        assertTrue(exists, "已创建的计划名称应该存在");
        
        // 检查不存在的名称
        boolean notExists = planService.checkPlanNameExists("不存在的计划名称", null);
        assertFalse(notExists, "不存在的计划名称应该返回false");
    }

    @Test
    void testCopyPlan() {
        // 先创建计划
        planService.createPlan(testPlanDto);
        
        // 查询创建的计划
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName(testPlanDto.getPlanName());
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        StocktakingPlanVo planVo = page.getRecords().get(0);
        testPlanId = planVo.getPlanId();
        
        // 复制计划
        String newPlanId = planService.copyPlan(testPlanId, "复制的测试计划");
        assertNotNull(newPlanId, "复制计划应该成功");
        
        // 验证复制结果
        StocktakingPlanVo copiedPlan = planService.selectPlanById(newPlanId);
        assertNotNull(copiedPlan, "应该能查询到复制的计划");
        assertEquals("复制的测试计划", copiedPlan.getPlanName(), "复制的计划名称应该正确");
        assertEquals(AssetStocktakingPlan.Status.DRAFT, copiedPlan.getStatus(), 
                    "复制的计划状态应该是草稿");
    }

    @Test
    void testValidatePlanData() {
        // 测试有效数据
        boolean valid = planService.validatePlanData(testPlanDto);
        assertTrue(valid, "有效数据验证应该通过");
        
        // 测试无效数据 - 空名称
        StocktakingPlanDto invalidDto = new StocktakingPlanDto();
        invalidDto.setPlanName("");
        boolean invalid = planService.validatePlanData(invalidDto);
        assertFalse(invalid, "空名称验证应该失败");
        
        // 测试无效数据 - 结束时间早于开始时间
        StocktakingPlanDto invalidDateDto = new StocktakingPlanDto();
        invalidDateDto.setPlanName("测试");
        invalidDateDto.setPlanType(1);
        invalidDateDto.setStartDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        invalidDateDto.setEndDate(new Date());
        invalidDateDto.setResponsibleUserId(1L);
        
        boolean invalidDate = planService.validatePlanData(invalidDateDto);
        assertFalse(invalidDate, "结束时间早于开始时间验证应该失败");
    }
}
