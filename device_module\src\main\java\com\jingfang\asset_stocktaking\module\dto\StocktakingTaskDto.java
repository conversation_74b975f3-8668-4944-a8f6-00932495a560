package com.jingfang.asset_stocktaking.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点任务数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingTaskDto implements Serializable {

    /**
     * 盘点任务ID（编辑时使用）
     */
    private String taskId;

    /**
     * 盘点计划ID
     */
    @NotBlank(message = "盘点计划ID不能为空")
    private String planId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 分配给的用户ID
     */
    @NotNull(message = "分配用户不能为空")
    private Long assignedUserId;

    /**
     * 资产范围（JSON格式）
     */
    private String assetScope;

    /**
     * 预期盘点数量
     */
    private Integer expectedCount;

    /**
     * 任务分发配置
     */
    private TaskDistributionConfig distributionConfig;

    /**
     * 任务分发配置内部类
     */
    @Data
    public static class TaskDistributionConfig implements Serializable {
        
        /**
         * 分发方式：1-按部门，2-按位置，3-按资产数量，4-手动指定
         */
        private Integer distributionType;
        
        /**
         * 每个任务最大资产数量
         */
        private Integer maxAssetCount;
        
        /**
         * 分配的用户ID列表
         */
        private List<Long> assignedUserIds;
        
        /**
         * 是否自动分配
         */
        private Boolean autoAssign;
        
        /**
         * 优先级：1-高，2-中，3-低
         */
        private Integer priority;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 任务执行信息
     */
    private TaskExecutionInfo executionInfo;

    /**
     * 任务执行信息内部类
     */
    @Data
    public static class TaskExecutionInfo implements Serializable {
        
        /**
         * 开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;
        
        /**
         * 结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;
        
        /**
         * 实际盘点数量
         */
        private Integer actualCount;
        
        /**
         * 状态：1-待执行，2-执行中，3-已完成
         */
        private Integer status;
        
        /**
         * 执行备注
         */
        private String executionRemark;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
