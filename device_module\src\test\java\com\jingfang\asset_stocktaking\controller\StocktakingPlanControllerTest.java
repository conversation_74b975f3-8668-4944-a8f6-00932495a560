package com.jingfang.asset_stocktaking.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.Date;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 盘点计划控制器测试类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class StocktakingPlanControllerTest {

    @Resource
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private StocktakingPlanDto testPlanDto;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity())
                .build();
        
        objectMapper = new ObjectMapper();
        
        // 准备测试数据
        testPlanDto = new StocktakingPlanDto();
        testPlanDto.setPlanName("API测试盘点计划");
        testPlanDto.setPlanType(AssetStocktakingPlan.PlanType.FULL);
        testPlanDto.setStartDate(new Date());
        testPlanDto.setEndDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        testPlanDto.setResponsibleUserId(1L);
        testPlanDto.setRemark("这是一个API测试盘点计划");
        
        // 设置盘点范围
        StocktakingPlanDto.PlanScopeDetail scopeDetail = new StocktakingPlanDto.PlanScopeDetail();
        scopeDetail.setIncludeSubDept(true);
        scopeDetail.setMinValue(0.0);
        scopeDetail.setMaxValue(100000.0);
        testPlanDto.setScopeDetail(scopeDetail);
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testList() throws Exception {
        // 准备查询请求
        PlanSearchRequest request = new PlanSearchRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        
        mockMvc.perform(post("/asset/stocktaking/plan/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:add"})
    void testAdd() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPlanDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("创建盘点计划成功"));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:add"})
    void testAddWithInvalidData() throws Exception {
        // 测试无效数据
        StocktakingPlanDto invalidDto = new StocktakingPlanDto();
        invalidDto.setPlanName(""); // 空名称
        
        mockMvc.perform(post("/asset/stocktaking/plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:edit"})
    void testEdit() throws Exception {
        // 先创建计划
        mockMvc.perform(post("/asset/stocktaking/plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPlanDto)));
        
        // 修改计划数据
        testPlanDto.setPlanName("修改后的API测试计划");
        testPlanDto.setRemark("修改后的备注");
        
        mockMvc.perform(put("/asset/stocktaking/plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPlanDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("修改盘点计划成功"));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:remove"})
    void testRemove() throws Exception {
        mockMvc.perform(delete("/asset/stocktaking/plan/test-plan-id-1,test-plan-id-2"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testGetInfo() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/test-plan-id"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:edit"})
    void testSubmitForApproval() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/submit"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:approve"})
    void testApprove() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/approve")
                .param("approvalComment", "审批通过"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:approve"})
    void testReject() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/reject")
                .param("rejectReason", "需要修改盘点范围"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:start"})
    void testStart() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/start"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:complete"})
    void testComplete() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/complete"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:cancel"})
    void testCancel() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/cancel")
                .param("cancelReason", "计划变更"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:add"})
    void testCopy() throws Exception {
        mockMvc.perform(post("/asset/stocktaking/plan/test-plan-id/copy")
                .param("newPlanName", "复制的测试计划"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testCheckPlanName() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/checkName")
                .param("planName", "测试计划名称"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testGetPlansByUser() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/user/1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testGetExpiringPlans() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/expiring")
                .param("days", "7"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testGetOverduePlans() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/overdue"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:view"})
    void testGetStatusStatistics() throws Exception {
        mockMvc.perform(get("/asset/stocktaking/plan/statistics/status"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(authorities = {"stocktaking:plan:export"})
    void testExport() throws Exception {
        PlanSearchRequest request = new PlanSearchRequest();
        request.setPageNum(1);
        request.setPageSize(10000);
        
        mockMvc.perform(post("/asset/stocktaking/plan/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void testUnauthorizedAccess() throws Exception {
        // 测试未授权访问
        mockMvc.perform(get("/asset/stocktaking/plan/test-plan-id"))
                .andDo(print())
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(authorities = {"other:permission"})
    void testInsufficientPermissions() throws Exception {
        // 测试权限不足
        mockMvc.perform(get("/asset/stocktaking/plan/test-plan-id"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }
}
