package com.jingfang.web.controller.asset.stocktaking;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingDifferenceDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference;
import com.jingfang.asset_stocktaking.module.vo.StocktakingReportVo;
import com.jingfang.asset_stocktaking.service.StocktakingDifferenceService;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.poi.ExcelUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Arrays;
import java.util.List;

/**
 * 盘点差异控制器
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/asset/stocktaking/difference")
public class StocktakingDifferenceController extends BaseController {

    @Resource
    private StocktakingDifferenceService differenceService;

    /**
     * 查询盘点差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam String planId,
                             @RequestParam(required = false) Integer diffType,
                             @RequestParam(required = false) Integer handleStatus,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        IPage<AssetStocktakingDifference> page = differenceService.selectDifferenceList(
            planId, diffType, handleStatus, pageNum, pageSize);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 导出盘点差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:export')")
    @Log(title = "盘点差异", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam String planId,
                       @RequestParam(required = false) Integer diffType,
                       @RequestParam(required = false) Integer handleStatus) {
        List<AssetStocktakingDifference> differences = differenceService.exportDifferences(planId, diffType, handleStatus);
        ExcelUtil<AssetStocktakingDifference> util = new ExcelUtil<>(AssetStocktakingDifference.class);
        util.exportExcel(response, differences, "盘点差异数据");
    }

    /**
     * 获取盘点差异详细信息
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/{diffId}")
    public AjaxResult getInfo(@PathVariable("diffId") String diffId) {
        AssetStocktakingDifference difference = differenceService.getById(diffId);
        return success(difference);
    }

    /**
     * 新增盘点差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:add')")
    @Log(title = "盘点差异", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StocktakingDifferenceDto differenceDto) {
        if (differenceService.createDifference(differenceDto)) {
            return success("创建盘点差异成功");
        }
        return error("创建盘点差异失败");
    }

    /**
     * 修改盘点差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:edit')")
    @Log(title = "盘点差异", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StocktakingDifferenceDto differenceDto) {
        if (differenceService.editDifference(differenceDto)) {
            return success("修改盘点差异成功");
        }
        return error("修改盘点差异失败");
    }

    /**
     * 删除盘点差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:remove')")
    @Log(title = "盘点差异", businessType = BusinessType.DELETE)
    @DeleteMapping("/{diffIds}")
    public AjaxResult remove(@PathVariable String[] diffIds) {
        if (differenceService.batchDeleteDifferences(Arrays.asList(diffIds))) {
            return success("删除盘点差异成功");
        }
        return error("删除盘点差异失败");
    }

    /**
     * 执行差异分析
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:analyze')")
    @Log(title = "盘点差异", businessType = BusinessType.OTHER)
    @PostMapping("/analyze/{planId}")
    public AjaxResult analyze(@PathVariable("planId") String planId) {
        if (differenceService.analyzeDifferences(planId)) {
            return success("执行差异分析成功");
        }
        return error("执行差异分析失败");
    }

    /**
     * 处理差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:handle')")
    @Log(title = "盘点差异", businessType = BusinessType.UPDATE)
    @PostMapping("/{diffId}/handle")
    public AjaxResult handle(@PathVariable("diffId") String diffId,
                            @RequestBody StocktakingDifferenceDto.DifferenceHandleInfo handleInfo) {
        if (differenceService.handleDifference(diffId, handleInfo)) {
            return success("处理差异成功");
        }
        return error("处理差异失败");
    }

    /**
     * 批量处理差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:handle')")
    @Log(title = "盘点差异", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/handle")
    public AjaxResult batchHandle(@RequestParam String[] diffIds,
                                 @RequestBody StocktakingDifferenceDto.DifferenceHandleInfo handleInfo) {
        if (differenceService.batchHandleDifferences(Arrays.asList(diffIds), handleInfo)) {
            return success("批量处理差异成功");
        }
        return error("批量处理差异失败");
    }

    /**
     * 更新差异处理状态
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:handle')")
    @Log(title = "盘点差异", businessType = BusinessType.UPDATE)
    @PostMapping("/{diffId}/status")
    public AjaxResult updateStatus(@PathVariable("diffId") String diffId,
                                  @RequestParam Integer handleStatus,
                                  @RequestParam(required = false) String handleSuggestion) {
        if (differenceService.updateDifferenceHandle(diffId, handleStatus, handleSuggestion)) {
            return success("更新差异处理状态成功");
        }
        return error("更新差异处理状态失败");
    }

    /**
     * 批量更新差异处理状态
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:handle')")
    @Log(title = "盘点差异", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/status")
    public AjaxResult batchUpdateStatus(@RequestParam String[] diffIds,
                                       @RequestParam Integer handleStatus) {
        if (differenceService.batchUpdateHandleStatus(Arrays.asList(diffIds), handleStatus)) {
            return success("批量更新差异处理状态成功");
        }
        return error("批量更新差异处理状态失败");
    }

    /**
     * 根据计划ID查询差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/plan/{planId}")
    public AjaxResult getDifferencesByPlan(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectDifferenceByPlanId(planId);
        return success(differences);
    }

    /**
     * 查询盘盈差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/surplus/{planId}")
    public AjaxResult getSurplusDifferences(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectSurplusDifferences(planId);
        return success(differences);
    }

    /**
     * 查询盘亏差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/deficit/{planId}")
    public AjaxResult getDeficitDifferences(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectDeficitDifferences(planId);
        return success(differences);
    }

    /**
     * 查询状态差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/status/{planId}")
    public AjaxResult getStatusDifferences(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectStatusDifferences(planId);
        return success(differences);
    }

    /**
     * 查询位置差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/location/{planId}")
    public AjaxResult getLocationDifferences(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectLocationDifferences(planId);
        return success(differences);
    }

    /**
     * 查询待处理差异列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/pending/{planId}")
    public AjaxResult getPendingDifferences(@PathVariable("planId") String planId) {
        List<AssetStocktakingDifference> differences = differenceService.selectPendingDifferences(planId);
        return success(differences);
    }

    /**
     * 统计计划的差异情况
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/statistics/{planId}")
    public AjaxResult getDifferenceStatistics(@PathVariable("planId") String planId) {
        StocktakingReportVo.DifferenceStatistics statistics = differenceService.selectDifferenceStatistics(planId);
        return success(statistics);
    }

    /**
     * 统计各类型差异数量
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/statistics/type/{planId}")
    public AjaxResult getTypeStatistics(@PathVariable("planId") String planId) {
        List<java.util.Map<String, Object>> statistics = differenceService.countDifferenceByType(planId);
        return success(statistics);
    }

    /**
     * 统计各处理状态差异数量
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/statistics/handle/{planId}")
    public AjaxResult getHandleStatistics(@PathVariable("planId") String planId) {
        List<java.util.Map<String, Object>> statistics = differenceService.countDifferenceByHandleStatus(planId);
        return success(statistics);
    }

    /**
     * 查询部门差异统计
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/statistics/dept/{planId}")
    public AjaxResult getDeptStatistics(@PathVariable("planId") String planId) {
        List<StocktakingReportVo.DeptStatistics> statistics = differenceService.selectDeptDifferenceStatistics(planId);
        return success(statistics);
    }

    /**
     * 根据资产ID查询差异记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/asset/{assetId}")
    public AjaxResult getDifferenceByAsset(@PathVariable("assetId") String assetId,
                                          @RequestParam String planId) {
        AssetStocktakingDifference difference = differenceService.selectDifferenceByAssetId(assetId, planId);
        return success(difference);
    }

    /**
     * 检查资产是否存在差异
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:difference:view')")
    @GetMapping("/check/{assetId}")
    public AjaxResult checkAssetDifference(@PathVariable("assetId") String assetId,
                                          @RequestParam String planId) {
        boolean hasDifference = differenceService.checkAssetHasDifference(assetId, planId);
        return success(hasDifference);
    }
}
