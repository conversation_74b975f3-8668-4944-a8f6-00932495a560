package com.jingfang.asset_stocktaking.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点计划数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingPlanDto implements Serializable {

    /**
     * 盘点计划ID（编辑时使用）
     */
    private String planId;

    /**
     * 盘点计划名称
     */
    @NotBlank(message = "盘点计划名称不能为空")
    private String planName;

    /**
     * 盘点类型：1-全盘，2-部分盘点
     */
    @NotNull(message = "盘点类型不能为空")
    private Integer planType;

    /**
     * 盘点范围（JSON格式）
     */
    private String planScope;

    /**
     * 计划开始日期
     */
    @NotNull(message = "计划开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 计划结束日期
     */
    @NotNull(message = "计划结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 负责人ID
     */
    @NotNull(message = "负责人不能为空")
    private Long responsibleUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点范围详细信息
     */
    private PlanScopeDetail scopeDetail;

    /**
     * 盘点范围详细信息内部类
     */
    @Data
    public static class PlanScopeDetail implements Serializable {
        
        /**
         * 部门ID列表
         */
        private List<Long> deptIds;
        
        /**
         * 资产分类ID列表
         */
        private List<Integer> categoryIds;
        
        /**
         * 存放位置ID列表
         */
        private List<Integer> locationIds;
        
        /**
         * 资产状态列表
         */
        private List<Integer> statusList;
        
        /**
         * 资产ID列表（精确指定资产时使用）
         */
        private List<String> assetIds;
        
        /**
         * 是否包含子部门
         */
        private Boolean includeSubDept;
        
        /**
         * 最小价值
         */
        private Double minValue;
        
        /**
         * 最大价值
         */
        private Double maxValue;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
