<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_stocktaking.mapper.StocktakingDifferenceMapper">

    <!-- 分页查询盘点差异列表 -->
    <select id="selectDifferenceList" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference
        <where>
            <if test="planId != null and planId != ''">
                AND plan_id = #{planId}
            </if>
            <if test="diffType != null">
                AND diff_type = #{diffType}
            </if>
            <if test="handleStatus != null">
                AND handle_status = #{handleStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据计划ID查询差异列表 -->
    <select id="selectDifferenceByPlanId" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference WHERE plan_id = #{planId}
    </select>

    <!-- 根据差异类型查询差异列表 -->
    <select id="selectDifferenceByType" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND diff_type = #{diffType}
    </select>

    <!-- 根据处理状态查询差异列表 -->
    <select id="selectDifferenceByHandleStatus" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND handle_status = #{handleStatus}
    </select>

    <!-- 统计计划的差异情况 -->
    <select id="selectDifferenceStatistics" resultType="com.jingfang.asset_stocktaking.module.vo.StocktakingReportVo$DifferenceStatistics">
        SELECT 
            COUNT(*) as totalDifferences,
            SUM(CASE WHEN diff_type = 1 THEN 1 ELSE 0 END) as surplusCount,
            SUM(CASE WHEN diff_type = 2 THEN 1 ELSE 0 END) as deficitCount,
            SUM(CASE WHEN diff_type = 3 THEN 1 ELSE 0 END) as statusDiffCount,
            SUM(CASE WHEN diff_type = 4 THEN 1 ELSE 0 END) as locationDiffCount,
            COALESCE(surplus_value.total_value, 0) as surplusValue,
            COALESCE(deficit_value.total_value, 0) as deficitValue,
            SUM(CASE WHEN handle_status = 3 THEN 1 ELSE 0 END) as processedDifferences,
            SUM(CASE WHEN handle_status IN (1, 2) THEN 1 ELSE 0 END) as pendingDifferences
        FROM asset_stocktaking_difference d
        LEFT JOIN (
            SELECT 
                plan_id,
                SUM(CAST(JSON_EXTRACT(book_value, '$.assetValue') AS DECIMAL(15,2))) as total_value
            FROM asset_stocktaking_difference 
            WHERE plan_id = #{planId} AND diff_type = 1
            GROUP BY plan_id
        ) surplus_value ON d.plan_id = surplus_value.plan_id
        LEFT JOIN (
            SELECT 
                plan_id,
                SUM(CAST(JSON_EXTRACT(book_value, '$.assetValue') AS DECIMAL(15,2))) as total_value
            FROM asset_stocktaking_difference 
            WHERE plan_id = #{planId} AND diff_type = 2
            GROUP BY plan_id
        ) deficit_value ON d.plan_id = deficit_value.plan_id
        WHERE d.plan_id = #{planId}
        GROUP BY d.plan_id
    </select>

    <!-- 统计各类型差异数量 -->
    <select id="countDifferenceByType" resultType="java.util.Map">
        SELECT 
            diff_type,
            CASE diff_type
                WHEN 1 THEN '盘盈'
                WHEN 2 THEN '盘亏'
                WHEN 3 THEN '状态差异'
                WHEN 4 THEN '位置差异'
                ELSE '未知'
            END as diffTypeDesc,
            COUNT(*) as count
        FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId}
        GROUP BY diff_type
    </select>

    <!-- 统计各处理状态差异数量 -->
    <select id="countDifferenceByHandleStatus" resultType="java.util.Map">
        SELECT 
            handle_status,
            CASE handle_status
                WHEN 1 THEN '待处理'
                WHEN 2 THEN '处理中'
                WHEN 3 THEN '已处理'
                ELSE '未知'
            END as handleStatusDesc,
            COUNT(*) as count
        FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId}
        GROUP BY handle_status
    </select>

    <!-- 查询盘盈差异列表 -->
    <select id="selectSurplusDifferences" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND diff_type = 1
    </select>

    <!-- 查询盘亏差异列表 -->
    <select id="selectDeficitDifferences" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND diff_type = 2
    </select>

    <!-- 查询状态差异列表 -->
    <select id="selectStatusDifferences" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND diff_type = 3
    </select>

    <!-- 查询位置差异列表 -->
    <select id="selectLocationDifferences" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND diff_type = 4
    </select>

    <!-- 查询待处理差异列表 -->
    <select id="selectPendingDifferences" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE plan_id = #{planId} AND handle_status IN (1, 2)
    </select>

    <!-- 批量插入差异记录 -->
    <insert id="batchInsertDifferences">
        INSERT INTO asset_stocktaking_difference (
            diff_id, plan_id, asset_id, diff_type, book_value, actual_value, 
            diff_reason, handle_status, handle_suggestion, create_time
        ) VALUES
        <foreach collection="differences" item="diff" separator=",">
            (#{diff.diffId}, #{diff.planId}, #{diff.assetId}, #{diff.diffType}, 
             #{diff.bookValue}, #{diff.actualValue}, #{diff.diffReason}, 
             #{diff.handleStatus}, #{diff.handleSuggestion}, #{diff.createTime})
        </foreach>
    </insert>

    <!-- 批量更新差异处理状态 -->
    <update id="batchUpdateHandleStatus">
        UPDATE asset_stocktaking_difference 
        SET handle_status = #{handleStatus}
        WHERE diff_id IN
        <foreach collection="diffIds" item="diffId" open="(" separator="," close=")">
            #{diffId}
        </foreach>
    </update>

    <!-- 更新差异处理信息 -->
    <update id="updateDifferenceHandle">
        UPDATE asset_stocktaking_difference 
        SET handle_status = #{handleStatus}, handle_suggestion = #{handleSuggestion}
        WHERE diff_id = #{diffId}
    </update>

    <!-- 根据资产ID查询差异记录 -->
    <select id="selectDifferenceByAssetId" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference">
        SELECT * FROM asset_stocktaking_difference 
        WHERE asset_id = #{assetId} AND plan_id = #{planId}
    </select>

    <!-- 检查资产是否存在差异 -->
    <select id="checkAssetHasDifference" resultType="boolean">
        SELECT COUNT(*) > 0 FROM asset_stocktaking_difference 
        WHERE asset_id = #{assetId} AND plan_id = #{planId}
    </select>

    <!-- 删除计划的所有差异记录 -->
    <delete id="deleteDifferencesByPlanId">
        DELETE FROM asset_stocktaking_difference WHERE plan_id = #{planId}
    </delete>

    <!-- 查询部门差异统计 -->
    <select id="selectDeptDifferenceStatistics" resultType="com.jingfang.asset_stocktaking.module.vo.StocktakingReportVo$DeptStatistics">
        SELECT 
            d.dept_id as deptId,
            d.dept_name as deptName,
            COALESCE(asset_stats.total_assets, 0) as totalAssets,
            COALESCE(record_stats.inventoried_assets, 0) as inventoriedAssets,
            COALESCE(diff_stats.difference_assets, 0) as differenceAssets,
            CASE 
                WHEN COALESCE(asset_stats.total_assets, 0) = 0 THEN 0
                ELSE ROUND(COALESCE(record_stats.inventoried_assets, 0) * 100.0 / asset_stats.total_assets, 2)
            END as completionRate,
            CASE 
                WHEN COALESCE(record_stats.inventoried_assets, 0) = 0 THEN 0
                ELSE ROUND(COALESCE(diff_stats.difference_assets, 0) * 100.0 / record_stats.inventoried_assets, 2)
            END as differenceRate,
            COALESCE(diff_stats.surplus_count, 0) as surplusCount,
            COALESCE(diff_stats.deficit_count, 0) as deficitCount,
            COALESCE(asset_stats.total_value, 0) as totalValue
        FROM sys_dept d
        LEFT JOIN (
            SELECT 
                a.dept_id,
                COUNT(*) as total_assets,
                SUM(a.asset_value) as total_value
            FROM asset_ledger a
            INNER JOIN asset_stocktaking_task t ON JSON_CONTAINS(t.asset_scope, CONCAT('"', a.asset_id, '"'))
            WHERE t.plan_id = #{planId}
            GROUP BY a.dept_id
        ) asset_stats ON d.dept_id = asset_stats.dept_id
        LEFT JOIN (
            SELECT 
                a.dept_id,
                COUNT(DISTINCT r.asset_id) as inventoried_assets
            FROM asset_stocktaking_record r
            INNER JOIN asset_stocktaking_task t ON r.task_id = t.task_id
            INNER JOIN asset_ledger a ON r.asset_id = a.asset_id
            WHERE t.plan_id = #{planId}
            GROUP BY a.dept_id
        ) record_stats ON d.dept_id = record_stats.dept_id
        LEFT JOIN (
            SELECT 
                a.dept_id,
                COUNT(*) as difference_assets,
                SUM(CASE WHEN diff.diff_type = 1 THEN 1 ELSE 0 END) as surplus_count,
                SUM(CASE WHEN diff.diff_type = 2 THEN 1 ELSE 0 END) as deficit_count
            FROM asset_stocktaking_difference diff
            INNER JOIN asset_ledger a ON diff.asset_id = a.asset_id
            WHERE diff.plan_id = #{planId}
            GROUP BY a.dept_id
        ) diff_stats ON d.dept_id = diff_stats.dept_id
        WHERE asset_stats.total_assets > 0
        ORDER BY d.dept_name
    </select>

</mapper>
