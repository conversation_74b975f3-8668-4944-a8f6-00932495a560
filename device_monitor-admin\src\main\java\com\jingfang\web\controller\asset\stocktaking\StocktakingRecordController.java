package com.jingfang.web.controller.asset.stocktaking;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingRecordDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord;
import com.jingfang.asset_stocktaking.module.request.RecordSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingRecordVo;
import com.jingfang.asset_stocktaking.service.StocktakingRecordService;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 盘点记录控制器
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/asset/stocktaking/record")
public class StocktakingRecordController extends BaseController {

    @Resource
    private StocktakingRecordService recordService;

    /**
     * 查询盘点记录列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody RecordSearchRequest request) {
        startPage();
        IPage<StocktakingRecordVo> page = recordService.selectRecordList(request);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 导出盘点记录列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:export')")
    @Log(title = "盘点记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody RecordSearchRequest request) {
        List<StocktakingRecordVo> records = recordService.exportRecords(request);
        ExcelUtil<StocktakingRecordVo> util = new ExcelUtil<>(StocktakingRecordVo.class);
        util.exportExcel(response, records, "盘点记录数据");
    }

    /**
     * 获取盘点记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") String recordId) {
        StocktakingRecordVo recordVo = recordService.selectRecordById(recordId);
        return success(recordVo);
    }

    /**
     * 新增盘点记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:add')")
    @Log(title = "盘点记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StocktakingRecordDto recordDto) {
        if (recordService.createRecord(recordDto)) {
            return success("创建盘点记录成功");
        }
        return error("创建盘点记录失败");
    }

    /**
     * 批量新增盘点记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:add')")
    @Log(title = "盘点记录", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@Validated @RequestBody StocktakingRecordDto recordDto) {
        if (recordService.batchCreateRecords(recordDto)) {
            return success("批量创建盘点记录成功");
        }
        return error("批量创建盘点记录失败");
    }

    /**
     * 修改盘点记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:edit')")
    @Log(title = "盘点记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StocktakingRecordDto recordDto) {
        if (recordService.editRecord(recordDto)) {
            return success("修改盘点记录成功");
        }
        return error("修改盘点记录失败");
    }

    /**
     * 删除盘点记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:remove')")
    @Log(title = "盘点记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable String[] recordIds) {
        if (recordService.batchDeleteRecords(Arrays.asList(recordIds))) {
            return success("删除盘点记录成功");
        }
        return error("删除盘点记录失败");
    }

    /**
     * 扫码盘点
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:scan')")
    @Log(title = "盘点记录", businessType = BusinessType.INSERT)
    @PostMapping("/scan")
    public AjaxResult scanInventory(@RequestParam String taskId, 
                                   @RequestParam String scanContent) {
        Long userId = SecurityUtils.getUserId();
        StocktakingRecordVo recordVo = recordService.scanInventory(taskId, scanContent, userId);
        if (recordVo != null) {
            return success("扫码盘点成功", recordVo);
        }
        return error("扫码盘点失败");
    }

    /**
     * 手动录入盘点结果
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:manual')")
    @Log(title = "盘点记录", businessType = BusinessType.INSERT)
    @PostMapping("/manual")
    public AjaxResult manualInventory(@Validated @RequestBody StocktakingRecordDto recordDto) {
        if (recordService.manualInventory(recordDto)) {
            return success("手动录入盘点结果成功");
        }
        return error("手动录入盘点结果失败");
    }

    /**
     * 根据任务ID查询记录列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getRecordsByTask(@PathVariable("taskId") String taskId) {
        List<AssetStocktakingRecord> records = recordService.selectRecordByTaskId(taskId);
        return success(records);
    }

    /**
     * 根据资产ID查询记录列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/asset/{assetId}")
    public AjaxResult getRecordsByAsset(@PathVariable("assetId") String assetId) {
        List<AssetStocktakingRecord> records = recordService.selectRecordByAssetId(assetId);
        return success(records);
    }

    /**
     * 查询异常记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/abnormal/{taskId}")
    public AjaxResult getAbnormalRecords(@PathVariable("taskId") String taskId) {
        List<AssetStocktakingRecord> records = recordService.selectAbnormalRecords(taskId);
        return success(records);
    }

    /**
     * 查询差异记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/difference/{taskId}")
    public AjaxResult getDifferenceRecords(@PathVariable("taskId") String taskId) {
        List<StocktakingRecordVo> records = recordService.selectDifferenceRecords(taskId);
        return success(records);
    }

    /**
     * 统计任务的记录情况
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/statistics/task/{taskId}")
    public AjaxResult getTaskStatistics(@PathVariable("taskId") String taskId) {
        Map<String, Object> statistics = recordService.countRecordsByTask(taskId);
        return success(statistics);
    }

    /**
     * 统计计划的记录情况
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/statistics/plan/{planId}")
    public AjaxResult getPlanStatistics(@PathVariable("planId") String planId) {
        Map<String, Object> statistics = recordService.countRecordsByPlan(planId);
        return success(statistics);
    }

    /**
     * 查询重复盘点的资产
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/duplicate/{planId}")
    public AjaxResult getDuplicateRecords(@PathVariable("planId") String planId) {
        List<Map<String, Object>> duplicates = recordService.selectDuplicateRecords(planId);
        return success(duplicates);
    }

    /**
     * 查询未盘点的资产
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/missing/{taskId}")
    public AjaxResult getMissingAssets(@PathVariable("taskId") String taskId) {
        List<Map<String, Object>> missing = recordService.selectMissingAssets(taskId);
        return success(missing);
    }

    /**
     * 检查资产是否已盘点
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/check")
    public AjaxResult checkAssetInventoried(@RequestParam String assetId, 
                                           @RequestParam String taskId) {
        boolean inventoried = recordService.checkAssetInventoried(assetId, taskId);
        return success(inventoried);
    }

    /**
     * 根据资产编码查询记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/code/{assetCode}")
    public AjaxResult getRecordByAssetCode(@PathVariable("assetCode") String assetCode,
                                          @RequestParam String taskId) {
        AssetStocktakingRecord record = recordService.selectRecordByAssetCode(assetCode, taskId);
        return success(record);
    }

    /**
     * 查询任务的盘点进度
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:view')")
    @GetMapping("/progress/{taskId}")
    public AjaxResult getTaskProgress(@PathVariable("taskId") String taskId) {
        Map<String, Object> progress = recordService.selectTaskInventoryProgress(taskId);
        return success(progress);
    }

    /**
     * 批量更新记录状态
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:edit')")
    @Log(title = "盘点记录", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/status")
    public AjaxResult batchUpdateStatus(@RequestParam String[] recordIds,
                                       @RequestParam Integer foundStatus) {
        if (recordService.batchUpdateFoundStatus(Arrays.asList(recordIds), foundStatus)) {
            return success("批量更新记录状态成功");
        }
        return error("批量更新记录状态失败");
    }

    /**
     * 导入盘点记录
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:record:import')")
    @Log(title = "盘点记录", businessType = BusinessType.IMPORT)
    @PostMapping("/import/{taskId}")
    public AjaxResult importRecords(@PathVariable("taskId") String taskId,
                                   @RequestParam("file") MultipartFile file) {
        try {
            // TODO: 实现Excel文件解析和导入逻辑
            // ExcelUtil<StocktakingRecordDto> util = new ExcelUtil<>(StocktakingRecordDto.class);
            // List<StocktakingRecordDto> records = util.importExcel(file.getInputStream());
            // Map<String, Object> result = recordService.importRecords(taskId, records);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", true);
            result.put("message", "导入功能待实现");
            
            return success(result);
        } catch (Exception e) {
            return error("导入盘点记录失败: " + e.getMessage());
        }
    }
}
