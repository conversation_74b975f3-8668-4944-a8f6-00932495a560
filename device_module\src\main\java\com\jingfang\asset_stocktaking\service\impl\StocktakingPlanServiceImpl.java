package com.jingfang.asset_stocktaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_stocktaking.mapper.StocktakingPlanMapper;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;
import com.jingfang.asset_stocktaking.service.StocktakingPlanService;
import com.jingfang.asset_stocktaking.service.StocktakingTaskService;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 盘点计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class StocktakingPlanServiceImpl extends ServiceImpl<StocktakingPlanMapper, AssetStocktakingPlan> 
        implements StocktakingPlanService {

    @Resource
    private StocktakingPlanMapper planMapper;

    @Resource
    private StocktakingTaskService taskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createPlan(StocktakingPlanDto planDto) {
        try {
            // 验证数据
            if (!validatePlanData(planDto)) {
                return false;
            }

            // 检查计划名称是否重复
            if (checkPlanNameExists(planDto.getPlanName(), null)) {
                log.warn("盘点计划名称已存在: {}", planDto.getPlanName());
                return false;
            }

            // 创建计划实体
            AssetStocktakingPlan plan = new AssetStocktakingPlan();
            BeanUtils.copyProperties(planDto, plan);
            
            plan.setPlanId(IdUtils.fastSimpleUUID());
            plan.setStatus(AssetStocktakingPlan.Status.DRAFT);
            plan.setCreateBy(SecurityUtils.getUsername());
            plan.setCreateTime(new Date());

            // 处理盘点范围
            if (planDto.getScopeDetail() != null) {
                plan.setPlanScope(JSON.toJSONString(planDto.getScopeDetail()));
            }

            return save(plan);
        } catch (Exception e) {
            log.error("创建盘点计划失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editPlan(StocktakingPlanDto planDto) {
        try {
            // 验证数据
            if (!validatePlanData(planDto)) {
                return false;
            }

            // 检查计划是否存在
            AssetStocktakingPlan existingPlan = getById(planDto.getPlanId());
            if (existingPlan == null) {
                log.warn("盘点计划不存在: {}", planDto.getPlanId());
                return false;
            }

            // 检查计划状态是否允许编辑
            if (existingPlan.getStatus() != AssetStocktakingPlan.Status.DRAFT) {
                log.warn("盘点计划状态不允许编辑: {}", existingPlan.getStatus());
                return false;
            }

            // 检查计划名称是否重复
            if (checkPlanNameExists(planDto.getPlanName(), planDto.getPlanId())) {
                log.warn("盘点计划名称已存在: {}", planDto.getPlanName());
                return false;
            }

            // 更新计划实体
            AssetStocktakingPlan plan = new AssetStocktakingPlan();
            BeanUtils.copyProperties(planDto, plan);
            
            plan.setUpdateBy(SecurityUtils.getUsername());
            plan.setUpdateTime(new Date());

            // 处理盘点范围
            if (planDto.getScopeDetail() != null) {
                plan.setPlanScope(JSON.toJSONString(planDto.getScopeDetail()));
            }

            return updateById(plan);
        } catch (Exception e) {
            log.error("编辑盘点计划失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlan(String planId) {
        try {
            // 检查计划是否存在
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null) {
                log.warn("盘点计划不存在: {}", planId);
                return false;
            }

            // 检查计划状态是否允许删除
            if (plan.getStatus() == AssetStocktakingPlan.Status.IN_PROGRESS) {
                log.warn("执行中的盘点计划不允许删除: {}", planId);
                return false;
            }

            // 删除相关任务
            taskService.remove(taskService.lambdaQuery().eq(AssetStocktakingTask::getPlanId, planId).getWrapper());

            return removeById(planId);
        } catch (Exception e) {
            log.error("删除盘点计划失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePlans(List<String> planIds) {
        try {
            for (String planId : planIds) {
                if (!deletePlan(planId)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除盘点计划失败", e);
            return false;
        }
    }

    @Override
    public IPage<StocktakingPlanVo> selectPlanList(PlanSearchRequest request) {
        Page<StocktakingPlanVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return planMapper.selectPlanList(page, request);
    }

    @Override
    public StocktakingPlanVo selectPlanById(String planId) {
        StocktakingPlanVo planVo = planMapper.selectPlanById(planId);
        if (planVo != null) {
            // 查询统计信息
            StocktakingPlanVo.PlanStatistics statistics = planMapper.selectPlanStatistics(planId);
            planVo.setStatistics(statistics);
        }
        return planVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitForApproval(String planId) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() != AssetStocktakingPlan.Status.DRAFT) {
                return false;
            }

            return planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.PENDING_APPROVAL, 
                                             SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("提交审批失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approvePlan(String planId, String approvalComment) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() != AssetStocktakingPlan.Status.PENDING_APPROVAL) {
                return false;
            }

            // 更新计划状态为执行中
            boolean result = planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.IN_PROGRESS, 
                                                        SecurityUtils.getUsername()) > 0;

            if (result) {
                // 自动分发任务
                taskService.distributeTasksByPlan(planId);
            }

            return result;
        } catch (Exception e) {
            log.error("审批通过失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectPlan(String planId, String rejectReason) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() != AssetStocktakingPlan.Status.PENDING_APPROVAL) {
                return false;
            }

            return planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.DRAFT, 
                                             SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("审批拒绝失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startPlan(String planId) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() != AssetStocktakingPlan.Status.PENDING_APPROVAL) {
                return false;
            }

            boolean result = planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.IN_PROGRESS, 
                                                        SecurityUtils.getUsername()) > 0;

            if (result) {
                // 自动分发任务
                taskService.distributeTasksByPlan(planId);
            }

            return result;
        } catch (Exception e) {
            log.error("启动盘点计划失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completePlan(String planId) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() != AssetStocktakingPlan.Status.IN_PROGRESS) {
                return false;
            }

            return planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.COMPLETED, 
                                             SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("完成盘点计划失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelPlan(String planId, String cancelReason) {
        try {
            AssetStocktakingPlan plan = getById(planId);
            if (plan == null || plan.getStatus() == AssetStocktakingPlan.Status.COMPLETED) {
                return false;
            }

            return planMapper.updatePlanStatus(planId, AssetStocktakingPlan.Status.CANCELLED, 
                                             SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("取消盘点计划失败", e);
            return false;
        }
    }

    @Override
    public boolean checkPlanNameExists(String planName, String excludePlanId) {
        return planMapper.checkPlanNameExists(planName, excludePlanId) > 0;
    }

    @Override
    public List<AssetStocktakingPlan> selectPlanByUserPermission(Long userId) {
        return planMapper.selectPlanByUserPermission(userId, null);
    }

    @Override
    public List<AssetStocktakingPlan> selectExpiringPlans(Integer days) {
        return planMapper.selectExpiringPlans(days);
    }

    @Override
    public List<AssetStocktakingPlan> selectOverduePlans() {
        return planMapper.selectOverduePlans();
    }

    @Override
    public List<java.util.Map<String, Object>> countPlanByStatus() {
        return planMapper.countPlanByStatus();
    }

    @Override
    public List<String> generateAssetListByScope(StocktakingPlanDto planDto) {
        // TODO: 根据盘点范围生成资产列表的具体实现
        // 这里需要根据部门、分类、位置等条件查询资产
        return null;
    }

    @Override
    public boolean validatePlanData(StocktakingPlanDto planDto) {
        if (StringUtils.isEmpty(planDto.getPlanName())) {
            return false;
        }
        if (planDto.getPlanType() == null) {
            return false;
        }
        if (planDto.getStartDate() == null || planDto.getEndDate() == null) {
            return false;
        }
        if (planDto.getStartDate().after(planDto.getEndDate())) {
            return false;
        }
        if (planDto.getResponsibleUserId() == null) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyPlan(String sourcePlanId, String newPlanName) {
        try {
            AssetStocktakingPlan sourcePlan = getById(sourcePlanId);
            if (sourcePlan == null) {
                return null;
            }

            AssetStocktakingPlan newPlan = new AssetStocktakingPlan();
            BeanUtils.copyProperties(sourcePlan, newPlan);
            
            newPlan.setPlanId(IdUtils.fastSimpleUUID());
            newPlan.setPlanName(newPlanName);
            newPlan.setStatus(AssetStocktakingPlan.Status.DRAFT);
            newPlan.setCreateBy(SecurityUtils.getUsername());
            newPlan.setCreateTime(new Date());
            newPlan.setUpdateBy(null);
            newPlan.setUpdateTime(null);

            if (save(newPlan)) {
                return newPlan.getPlanId();
            }
            return null;
        } catch (Exception e) {
            log.error("复制盘点计划失败", e);
            return null;
        }
    }
}
