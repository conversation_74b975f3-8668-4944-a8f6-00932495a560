package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.dto.StocktakingTaskDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.request.TaskSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;
import com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 盘点任务服务测试类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StocktakingTaskServiceTest {

    @Resource
    private StocktakingTaskService taskService;

    @Resource
    private StocktakingPlanService planService;

    private String testPlanId;
    private StocktakingTaskDto testTaskDto;

    @BeforeEach
    void setUp() {
        // 先创建测试计划
        StocktakingPlanDto planDto = new StocktakingPlanDto();
        planDto.setPlanName("任务测试计划");
        planDto.setPlanType(AssetStocktakingPlan.PlanType.FULL);
        planDto.setStartDate(new Date());
        planDto.setEndDate(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        planDto.setResponsibleUserId(1L);
        
        planService.createPlan(planDto);
        
        // 查询创建的计划ID
        PlanSearchRequest searchRequest = new PlanSearchRequest();
        searchRequest.setPlanName("任务测试计划");
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingPlanVo> page = planService.selectPlanList(searchRequest);
        testPlanId = page.getRecords().get(0).getPlanId();
        
        // 准备测试任务数据
        testTaskDto = new StocktakingTaskDto();
        testTaskDto.setPlanId(testPlanId);
        testTaskDto.setTaskName("测试盘点任务");
        testTaskDto.setAssignedUserId(1L);
        testTaskDto.setExpectedCount(10);
        
        // 设置分发配置
        StocktakingTaskDto.TaskDistributionConfig config = new StocktakingTaskDto.TaskDistributionConfig();
        config.setDistributionType(1);
        config.setMaxAssetCount(50);
        config.setAutoAssign(true);
        config.setPriority(2);
        testTaskDto.setDistributionConfig(config);
    }

    @Test
    void testCreateTask() {
        // 测试创建任务
        boolean result = taskService.createTask(testTaskDto);
        assertTrue(result, "创建任务应该成功");
    }

    @Test
    void testCreateTaskWithInvalidData() {
        // 测试无效数据创建任务
        StocktakingTaskDto invalidDto = new StocktakingTaskDto();
        invalidDto.setPlanId(""); // 空计划ID
        
        boolean result = taskService.createTask(invalidDto);
        assertFalse(result, "无效数据创建任务应该失败");
    }

    @Test
    void testEditTask() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        assertFalse(page.getRecords().isEmpty(), "应该能查询到创建的任务");
        
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 修改任务
        testTaskDto.setTaskId(taskId);
        testTaskDto.setTaskName("修改后的任务名称");
        testTaskDto.setExpectedCount(20);
        
        boolean result = taskService.editTask(testTaskDto);
        assertTrue(result, "修改任务应该成功");
        
        // 验证修改结果
        StocktakingTaskVo updatedTask = taskService.selectTaskById(taskId);
        assertEquals("修改后的任务名称", updatedTask.getTaskName(), "任务名称应该已修改");
        assertEquals(20, updatedTask.getExpectedCount(), "预期数量应该已修改");
    }

    @Test
    void testDeleteTask() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 删除任务
        boolean result = taskService.deleteTask(taskId);
        assertTrue(result, "删除任务应该成功");
        
        // 验证删除结果
        StocktakingTaskVo deletedTask = taskService.selectTaskById(taskId);
        assertNull(deletedTask, "删除后应该查询不到任务");
    }

    @Test
    void testSelectTaskList() {
        // 创建多个测试任务
        for (int i = 1; i <= 3; i++) {
            StocktakingTaskDto dto = new StocktakingTaskDto();
            dto.setPlanId(testPlanId);
            dto.setTaskName("测试任务" + i);
            dto.setAssignedUserId(1L);
            dto.setExpectedCount(10 * i);
            taskService.createTask(dto);
        }
        
        // 查询任务列表
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        assertTrue(page.getRecords().size() >= 3, "应该查询到至少3个任务");
    }

    @Test
    void testClaimTask() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 领取任务
        boolean result = taskService.claimTask(taskId, 2L);
        assertTrue(result, "领取任务应该成功");
        
        // 验证领取结果
        StocktakingTaskVo claimedTask = taskService.selectTaskById(taskId);
        assertEquals(2L, claimedTask.getAssignedUserId(), "任务应该分配给指定用户");
    }

    @Test
    void testStartTask() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 开始任务
        boolean result = taskService.startTask(taskId);
        assertTrue(result, "开始任务应该成功");
        
        // 验证状态变更
        StocktakingTaskVo startedTask = taskService.selectTaskById(taskId);
        assertEquals(AssetStocktakingTask.Status.IN_PROGRESS, startedTask.getStatus(), 
                    "任务状态应该变为执行中");
        assertNotNull(startedTask.getStartTime(), "开始时间应该已设置");
    }

    @Test
    void testCompleteTask() {
        // 先创建并开始任务
        taskService.createTask(testTaskDto);
        
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        taskService.startTask(taskId);
        
        // 完成任务
        boolean result = taskService.completeTask(taskId);
        assertTrue(result, "完成任务应该成功");
        
        // 验证状态变更
        StocktakingTaskVo completedTask = taskService.selectTaskById(taskId);
        assertEquals(AssetStocktakingTask.Status.COMPLETED, completedTask.getStatus(), 
                    "任务状态应该变为已完成");
        assertNotNull(completedTask.getEndTime(), "结束时间应该已设置");
    }

    @Test
    void testReassignTask() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 重新分配任务
        boolean result = taskService.reassignTask(taskId, 3L);
        assertTrue(result, "重新分配任务应该成功");
        
        // 验证分配结果
        StocktakingTaskVo reassignedTask = taskService.selectTaskById(taskId);
        assertEquals(3L, reassignedTask.getAssignedUserId(), "任务应该重新分配给指定用户");
    }

    @Test
    void testCalculateProgress() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 计算进度
        StocktakingTaskVo.TaskProgress progress = taskService.calculateProgress(taskId);
        assertNotNull(progress, "应该能计算任务进度");
        assertEquals(0.0, progress.getProgressPercentage(), "初始进度应该为0");
    }

    @Test
    void testUpdateTaskProgress() {
        // 先创建任务
        taskService.createTask(testTaskDto);
        
        // 查询创建的任务
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        StocktakingTaskVo taskVo = page.getRecords().get(0);
        String taskId = taskVo.getTaskId();
        
        // 更新任务进度
        boolean result = taskService.updateTaskProgress(taskId, 5);
        assertTrue(result, "更新任务进度应该成功");
        
        // 验证进度更新
        StocktakingTaskVo updatedTask = taskService.selectTaskById(taskId);
        assertEquals(5, updatedTask.getActualCount(), "实际数量应该已更新");
    }

    @Test
    void testSelectTaskByPlanId() {
        // 创建多个任务
        for (int i = 1; i <= 3; i++) {
            StocktakingTaskDto dto = new StocktakingTaskDto();
            dto.setPlanId(testPlanId);
            dto.setTaskName("计划任务" + i);
            dto.setAssignedUserId(1L);
            taskService.createTask(dto);
        }
        
        // 查询计划下的任务
        List<AssetStocktakingTask> tasks = taskService.selectTaskByPlanId(testPlanId);
        assertTrue(tasks.size() >= 3, "应该查询到至少3个任务");
    }

    @Test
    void testValidateTaskData() {
        // 测试有效数据
        boolean valid = taskService.validateTaskData(testTaskDto);
        assertTrue(valid, "有效数据验证应该通过");
        
        // 测试无效数据 - 空计划ID
        StocktakingTaskDto invalidDto = new StocktakingTaskDto();
        invalidDto.setPlanId("");
        boolean invalid = taskService.validateTaskData(invalidDto);
        assertFalse(invalid, "空计划ID验证应该失败");
        
        // 测试无效数据 - 空任务名称
        StocktakingTaskDto invalidNameDto = new StocktakingTaskDto();
        invalidNameDto.setPlanId(testPlanId);
        invalidNameDto.setTaskName("");
        boolean invalidName = taskService.validateTaskData(invalidNameDto);
        assertFalse(invalidName, "空任务名称验证应该失败");
    }

    @Test
    void testBatchUpdateTaskStatus() {
        // 创建多个任务
        for (int i = 1; i <= 3; i++) {
            StocktakingTaskDto dto = new StocktakingTaskDto();
            dto.setPlanId(testPlanId);
            dto.setTaskName("批量任务" + i);
            dto.setAssignedUserId(1L);
            taskService.createTask(dto);
        }
        
        // 查询任务列表
        TaskSearchRequest searchRequest = new TaskSearchRequest();
        searchRequest.setPlanId(testPlanId);
        searchRequest.setPageNum(1);
        searchRequest.setPageSize(10);
        
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(searchRequest);
        List<String> taskIds = page.getRecords().stream()
                .map(StocktakingTaskVo::getTaskId)
                .collect(java.util.stream.Collectors.toList());
        
        // 批量更新状态
        boolean result = taskService.batchUpdateTaskStatus(taskIds, AssetStocktakingTask.Status.IN_PROGRESS);
        assertTrue(result, "批量更新任务状态应该成功");
    }
}
