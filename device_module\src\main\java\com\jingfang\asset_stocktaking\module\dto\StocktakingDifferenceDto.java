package com.jingfang.asset_stocktaking.module.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 盘点差异数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingDifferenceDto implements Serializable {

    /**
     * 差异ID（编辑时使用）
     */
    private String diffId;

    /**
     * 盘点计划ID
     */
    @NotBlank(message = "盘点计划ID不能为空")
    private String planId;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 差异类型：1-盘盈，2-盘亏，3-状态差异，4-位置差异
     */
    @NotNull(message = "差异类型不能为空")
    private Integer diffType;

    /**
     * 差异原因
     */
    private String diffReason;

    /**
     * 处理状态：1-待处理，2-处理中，3-已处理
     */
    private Integer handleStatus;

    /**
     * 处理建议
     */
    private String handleSuggestion;

    /**
     * 账面信息
     */
    private BookValueInfo bookValue;

    /**
     * 实际信息
     */
    private ActualValueInfo actualValue;

    /**
     * 账面信息内部类
     */
    @Data
    public static class BookValueInfo implements Serializable {
        
        /**
         * 资产名称
         */
        private String assetName;
        
        /**
         * 资产编码
         */
        private String assetCode;
        
        /**
         * 资产状态
         */
        private Integer assetStatus;
        
        /**
         * 存放位置
         */
        private String location;
        
        /**
         * 使用部门
         */
        private String deptName;
        
        /**
         * 管理人员
         */
        private String managerName;
        
        /**
         * 资产价值
         */
        private Double assetValue;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 实际信息内部类
     */
    @Data
    public static class ActualValueInfo implements Serializable {
        
        /**
         * 实际状态
         */
        private Integer actualStatus;
        
        /**
         * 实际位置
         */
        private String actualLocation;
        
        /**
         * 发现状态：1-找到，0-未找到
         */
        private Integer foundStatus;
        
        /**
         * 盘点时间
         */
        private String inventoryTime;
        
        /**
         * 盘点人员
         */
        private String inventoryUser;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 差异处理信息
     */
    private DifferenceHandleInfo handleInfo;

    /**
     * 差异处理信息内部类
     */
    @Data
    public static class DifferenceHandleInfo implements Serializable {
        
        /**
         * 处理方式：1-台账更新，2-资产入库，3-资产核销，4-位置调整，5-状态更新
         */
        private Integer handleType;
        
        /**
         * 处理人员ID
         */
        private Long handleUserId;
        
        /**
         * 处理时间
         */
        private String handleTime;
        
        /**
         * 处理结果
         */
        private String handleResult;
        
        /**
         * 审批流程ID
         */
        private String approvalId;
        
        /**
         * 是否需要审批
         */
        private Boolean needApproval;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 批量差异处理
     */
    private List<String> batchDiffIds;

    private static final long serialVersionUID = 1L;
}
