package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 盘点报告视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingReportVo implements Serializable {

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 盘点计划名称
     */
    private String planName;

    /**
     * 报告生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 盘点汇总信息
     */
    private SummaryInfo summaryInfo;

    /**
     * 差异统计信息
     */
    private DifferenceStatistics differenceStatistics;

    /**
     * 部门统计信息
     */
    private List<DeptStatistics> deptStatisticsList;

    /**
     * 盘点汇总信息内部类
     */
    @Data
    public static class SummaryInfo implements Serializable {
        
        /**
         * 计划开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date planStartDate;
        
        /**
         * 计划结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date planEndDate;
        
        /**
         * 实际开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date actualStartTime;
        
        /**
         * 实际结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date actualEndTime;
        
        /**
         * 负责人姓名
         */
        private String responsibleUserName;
        
        /**
         * 参与人数
         */
        private Integer participantCount;
        
        /**
         * 总任务数
         */
        private Integer totalTasks;
        
        /**
         * 已完成任务数
         */
        private Integer completedTasks;
        
        /**
         * 应盘资产总数
         */
        private Integer totalAssets;
        
        /**
         * 实盘资产总数
         */
        private Integer inventoriedAssets;
        
        /**
         * 盘点完成率
         */
        private Double completionRate;
        
        /**
         * 应盘资产总值
         */
        private BigDecimal totalAssetValue;
        
        /**
         * 实盘资产总值
         */
        private BigDecimal inventoriedAssetValue;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 差异统计信息内部类
     */
    @Data
    public static class DifferenceStatistics implements Serializable {
        
        /**
         * 差异资产总数
         */
        private Integer totalDifferences;
        
        /**
         * 盘盈数量
         */
        private Integer surplusCount;
        
        /**
         * 盘亏数量
         */
        private Integer deficitCount;
        
        /**
         * 状态差异数量
         */
        private Integer statusDiffCount;
        
        /**
         * 位置差异数量
         */
        private Integer locationDiffCount;
        
        /**
         * 盘盈价值
         */
        private BigDecimal surplusValue;
        
        /**
         * 盘亏价值
         */
        private BigDecimal deficitValue;
        
        /**
         * 差异率
         */
        private Double differenceRate;
        
        /**
         * 已处理差异数
         */
        private Integer processedDifferences;
        
        /**
         * 待处理差异数
         */
        private Integer pendingDifferences;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 部门统计信息内部类
     */
    @Data
    public static class DeptStatistics implements Serializable {
        
        /**
         * 部门ID
         */
        private Long deptId;
        
        /**
         * 部门名称
         */
        private String deptName;
        
        /**
         * 应盘资产数
         */
        private Integer totalAssets;
        
        /**
         * 实盘资产数
         */
        private Integer inventoriedAssets;
        
        /**
         * 差异资产数
         */
        private Integer differenceAssets;
        
        /**
         * 完成率
         */
        private Double completionRate;
        
        /**
         * 差异率
         */
        private Double differenceRate;
        
        /**
         * 盘盈数量
         */
        private Integer surplusCount;
        
        /**
         * 盘亏数量
         */
        private Integer deficitCount;
        
        /**
         * 资产总值
         */
        private BigDecimal totalValue;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
