package com.jingfang.asset_stocktaking.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_stocktaking.mapper.StocktakingRecordMapper;
import com.jingfang.asset_stocktaking.module.dto.StocktakingRecordDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord;
import com.jingfang.asset_stocktaking.module.request.RecordSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingRecordVo;
import com.jingfang.asset_stocktaking.service.StocktakingRecordService;
import com.jingfang.asset_stocktaking.service.StocktakingTaskService;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 盘点记录服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class StocktakingRecordServiceImpl extends ServiceImpl<StocktakingRecordMapper, AssetStocktakingRecord> 
        implements StocktakingRecordService {

    @Resource
    private StocktakingRecordMapper recordMapper;

    @Resource
    private StocktakingTaskService taskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRecord(StocktakingRecordDto recordDto) {
        try {
            // 验证数据
            if (!validateRecordData(recordDto)) {
                return false;
            }

            // 检查资产是否已盘点
            if (checkAssetInventoried(recordDto.getAssetId(), recordDto.getTaskId())) {
                log.warn("资产已盘点: {}", recordDto.getAssetId());
                return false;
            }

            // 创建记录实体
            AssetStocktakingRecord record = new AssetStocktakingRecord();
            BeanUtils.copyProperties(recordDto, record);
            
            record.setRecordId(IdUtils.fastSimpleUUID());
            record.setInventoryUserId(SecurityUtils.getUserId());
            record.setInventoryTime(new Date());

            boolean result = save(record);

            if (result) {
                // 更新任务进度
                updateTaskProgress(recordDto.getTaskId());
            }

            return result;
        } catch (Exception e) {
            log.error("创建盘点记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateRecords(StocktakingRecordDto recordDto) {
        try {
            if (recordDto.getBatchRecords() == null || recordDto.getBatchRecords().isEmpty()) {
                return false;
            }

            List<AssetStocktakingRecord> records = new ArrayList<>();
            Long userId = SecurityUtils.getUserId();
            Date now = new Date();

            for (StocktakingRecordDto.BatchRecordItem item : recordDto.getBatchRecords()) {
                // 检查资产是否已盘点
                if (checkAssetInventoried(item.getAssetId(), recordDto.getTaskId())) {
                    continue;
                }

                AssetStocktakingRecord record = new AssetStocktakingRecord();
                record.setRecordId(IdUtils.fastSimpleUUID());
                record.setTaskId(recordDto.getTaskId());
                record.setAssetId(item.getAssetId());
                record.setAssetCode(item.getAssetCode());
                record.setFoundStatus(item.getFoundStatus());
                record.setActualLocation(item.getActualLocation());
                record.setActualStatus(item.getActualStatus());
                record.setInventoryUserId(userId);
                record.setInventoryTime(now);
                record.setRemark(item.getRemark());

                records.add(record);
            }

            if (!records.isEmpty()) {
                boolean result = recordMapper.batchInsertRecords(records) > 0;
                if (result) {
                    // 更新任务进度
                    updateTaskProgress(recordDto.getTaskId());
                }
                return result;
            }

            return true;
        } catch (Exception e) {
            log.error("批量创建盘点记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editRecord(StocktakingRecordDto recordDto) {
        try {
            // 验证数据
            if (!validateRecordData(recordDto)) {
                return false;
            }

            // 检查记录是否存在
            AssetStocktakingRecord existingRecord = getById(recordDto.getRecordId());
            if (existingRecord == null) {
                log.warn("盘点记录不存在: {}", recordDto.getRecordId());
                return false;
            }

            // 更新记录实体
            AssetStocktakingRecord record = new AssetStocktakingRecord();
            BeanUtils.copyProperties(recordDto, record);

            return updateById(record);
        } catch (Exception e) {
            log.error("编辑盘点记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRecord(String recordId) {
        try {
            AssetStocktakingRecord record = getById(recordId);
            if (record == null) {
                log.warn("盘点记录不存在: {}", recordId);
                return false;
            }

            boolean result = removeById(recordId);
            if (result) {
                // 更新任务进度
                updateTaskProgress(record.getTaskId());
            }

            return result;
        } catch (Exception e) {
            log.error("删除盘点记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRecords(List<String> recordIds) {
        try {
            // 获取任务ID用于更新进度
            String taskId = null;
            if (!recordIds.isEmpty()) {
                AssetStocktakingRecord record = getById(recordIds.get(0));
                if (record != null) {
                    taskId = record.getTaskId();
                }
            }

            boolean result = removeByIds(recordIds);
            if (result && taskId != null) {
                // 更新任务进度
                updateTaskProgress(taskId);
            }

            return result;
        } catch (Exception e) {
            log.error("批量删除盘点记录失败", e);
            return false;
        }
    }

    @Override
    public IPage<StocktakingRecordVo> selectRecordList(RecordSearchRequest request) {
        Page<StocktakingRecordVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return recordMapper.selectRecordList(page, request);
    }

    @Override
    public StocktakingRecordVo selectRecordById(String recordId) {
        return recordMapper.selectRecordById(recordId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StocktakingRecordVo scanInventory(String taskId, String scanContent, Long userId) {
        try {
            // 根据扫码内容查找资产
            AssetStocktakingRecord existingRecord = recordMapper.selectRecordByAssetCode(scanContent, taskId);
            if (existingRecord != null) {
                log.warn("资产已盘点: {}", scanContent);
                return recordMapper.selectRecordById(existingRecord.getRecordId());
            }

            // TODO: 根据扫码内容查找资产信息
            // 这里需要与资产台账系统集成

            // 创建盘点记录
            AssetStocktakingRecord record = new AssetStocktakingRecord();
            record.setRecordId(IdUtils.fastSimpleUUID());
            record.setTaskId(taskId);
            record.setAssetCode(scanContent);
            record.setFoundStatus(AssetStocktakingRecord.FoundStatus.FOUND);
            record.setInventoryUserId(userId);
            record.setInventoryTime(new Date());

            if (save(record)) {
                // 更新任务进度
                updateTaskProgress(taskId);
                return recordMapper.selectRecordById(record.getRecordId());
            }

            return null;
        } catch (Exception e) {
            log.error("扫码盘点失败", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualInventory(StocktakingRecordDto recordDto) {
        return createRecord(recordDto);
    }

    @Override
    public List<AssetStocktakingRecord> selectRecordByTaskId(String taskId) {
        return recordMapper.selectRecordByTaskId(taskId);
    }

    @Override
    public List<AssetStocktakingRecord> selectRecordByAssetId(String assetId) {
        return recordMapper.selectRecordByAssetId(assetId);
    }

    @Override
    public List<AssetStocktakingRecord> selectAbnormalRecords(String taskId) {
        return recordMapper.selectAbnormalRecords(taskId);
    }

    @Override
    public List<StocktakingRecordVo> selectDifferenceRecords(String taskId) {
        return recordMapper.selectDifferenceRecords(taskId);
    }

    @Override
    public Map<String, Object> countRecordsByTask(String taskId) {
        return recordMapper.countRecordsByTask(taskId);
    }

    @Override
    public Map<String, Object> countRecordsByPlan(String planId) {
        return recordMapper.countRecordsByPlan(planId);
    }

    @Override
    public List<Map<String, Object>> selectDuplicateRecords(String planId) {
        return recordMapper.selectDuplicateRecords(planId);
    }

    @Override
    public List<Map<String, Object>> selectMissingAssets(String taskId) {
        return recordMapper.selectMissingAssets(taskId);
    }

    @Override
    public boolean checkAssetInventoried(String assetId, String taskId) {
        return recordMapper.checkAssetInventoried(assetId, taskId);
    }

    @Override
    public AssetStocktakingRecord selectRecordByAssetCode(String assetCode, String taskId) {
        return recordMapper.selectRecordByAssetCode(assetCode, taskId);
    }

    @Override
    public Map<String, Object> selectTaskInventoryProgress(String taskId) {
        return recordMapper.selectTaskInventoryProgress(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateFoundStatus(List<String> recordIds, Integer foundStatus) {
        try {
            return recordMapper.batchUpdateFoundStatus(recordIds, foundStatus) > 0;
        } catch (Exception e) {
            log.error("批量更新记录状态失败", e);
            return false;
        }
    }

    @Override
    public boolean validateRecordData(StocktakingRecordDto recordDto) {
        if (StringUtils.isEmpty(recordDto.getTaskId())) {
            return false;
        }
        if (StringUtils.isEmpty(recordDto.getAssetId()) && StringUtils.isEmpty(recordDto.getAssetCode())) {
            return false;
        }
        if (recordDto.getFoundStatus() == null) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importRecords(String taskId, List<StocktakingRecordDto> records) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        try {
            for (StocktakingRecordDto recordDto : records) {
                recordDto.setTaskId(taskId);
                if (createRecord(recordDto)) {
                    successCount++;
                } else {
                    failCount++;
                    errorMessages.add("资产编码 " + recordDto.getAssetCode() + " 导入失败");
                }
            }

            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);
            result.put("success", failCount == 0);

        } catch (Exception e) {
            log.error("导入盘点记录失败", e);
            result.put("success", false);
            result.put("errorMessage", "导入过程中发生异常");
        }

        return result;
    }

    @Override
    public List<StocktakingRecordVo> exportRecords(RecordSearchRequest request) {
        request.setPageNum(1);
        request.setPageSize(10000);
        IPage<StocktakingRecordVo> page = recordMapper.selectRecordList(new Page<>(1, 10000), request);
        return page.getRecords();
    }

    /**
     * 更新任务进度
     */
    private void updateTaskProgress(String taskId) {
        try {
            Map<String, Object> progress = recordMapper.selectTaskInventoryProgress(taskId);
            if (progress != null) {
                Integer actualCount = (Integer) progress.get("actualCount");
                taskService.updateTaskProgress(taskId, actualCount);
            }
        } catch (Exception e) {
            log.error("更新任务进度失败", e);
        }
    }
}
