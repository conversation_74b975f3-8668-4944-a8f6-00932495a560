package com.jingfang.asset_stocktaking.service;

import com.jingfang.asset_stocktaking.module.vo.StocktakingReportVo;

import java.util.List;

/**
 * 盘点报告服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface StocktakingReportService {

    /**
     * 生成盘点汇总报告
     * 
     * @param planId 计划ID
     * @return 汇总报告
     */
    StocktakingReportVo generateSummaryReport(String planId);

    /**
     * 生成差异明细报告
     * 
     * @param planId 计划ID
     * @return 差异明细报告数据
     */
    List<java.util.Map<String, Object>> generateDifferenceDetailReport(String planId);

    /**
     * 生成部门统计报告
     * 
     * @param planId 计划ID
     * @return 部门统计报告
     */
    List<StocktakingReportVo.DeptStatistics> generateDeptStatisticsReport(String planId);

    /**
     * 生成盘点进度报告
     * 
     * @param planId 计划ID
     * @return 进度报告数据
     */
    java.util.Map<String, Object> generateProgressReport(String planId);

    /**
     * 生成资产变动报告
     * 
     * @param planId 计划ID
     * @return 资产变动报告数据
     */
    List<java.util.Map<String, Object>> generateAssetChangeReport(String planId);



    /**
     * 生成自定义报告
     * 
     * @param planId 计划ID
     * @param reportConfig 报告配置
     * @return 自定义报告数据
     */
    java.util.Map<String, Object> generateCustomReport(String planId, java.util.Map<String, Object> reportConfig);

    /**
     * 获取报告模板列表
     * 
     * @return 模板列表
     */
    List<java.util.Map<String, Object>> getReportTemplates();

    /**
     * 保存报告模板
     * 
     * @param templateName 模板名称
     * @param templateConfig 模板配置
     * @return 是否成功
     */
    boolean saveReportTemplate(String templateName, java.util.Map<String, Object> templateConfig);

    /**
     * 删除报告模板
     * 
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean deleteReportTemplate(String templateId);

    /**
     * 生成盘点统计图表数据
     * 
     * @param planId 计划ID
     * @return 图表数据
     */
    java.util.Map<String, Object> generateChartData(String planId);

    /**
     * 生成多计划对比报告
     * 
     * @param planIds 计划ID列表
     * @return 对比报告数据
     */
    java.util.Map<String, Object> generateComparisonReport(List<String> planIds);

    /**
     * 生成趋势分析报告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势分析数据
     */
    java.util.Map<String, Object> generateTrendAnalysisReport(String startDate, String endDate);
}
